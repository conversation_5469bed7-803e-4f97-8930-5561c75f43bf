# PropertyGuru配置管理指南

## 🚀 快速添加新配置

### 方法1: 使用配置管理器（推荐）

```bash
# 交互式模式 - 最简单
python3 config_manager.py

# 命令行模式 - 最快速
python3 config_manager.py "你的PropertyGuru URL" "配置名称" "文件名"
```

### 方法2: 使用快速配置工具

```bash
# 交互式模式
python3 quick_config.py

# 命令行模式
python3 quick_config.py "URL" "配置名称" "文件名" "描述"
```

## 📋 配置添加步骤

### 1. 获取PropertyGuru URL
1. 访问 https://www.propertyguru.com.sg
2. 设置你的搜索条件（地铁站、价格、卧室等）
3. 复制完整的URL

### 2. 运行配置管理器
```bash
cd propertyguru_scraper_project
python3 config_manager.py
```

### 3. 输入信息
- **URL**: 粘贴PropertyGuru搜索URL
- **配置名称**: 如 "租赁 - 西南部区域 (3卧室)"
- **配置描述**: 详细说明这个配置的用途
- **文件名**: 如 "rent_southwest_3bed"（不含.json）

### 4. 确认保存
配置会自动保存到 `configs/` 目录，立即可用

## 🎯 示例

### 示例1: 租赁配置
```bash
python3 config_manager.py \
  "https://www.propertyguru.com.sg/property-for-rent?bedrooms=3&minPrice=3600&maxPrice=5500&mrtStations=EW22&mrtStations=EW23" \
  "租赁 - 西南部区域 (3卧室)" \
  "rent_southwest_3bed"
```

### 示例2: 销售配置
```bash
python3 config_manager.py \
  "https://www.propertyguru.com.sg/property-for-sale?bedrooms=4&minPrice=2000000&maxPrice=3000000&mrtStations=NS22" \
  "销售 - Orchard豪华公寓" \
  "sale_orchard_luxury"
```

## 📁 配置文件结构

生成的配置文件包含以下字段：

```json
{
  "name": "配置显示名称",
  "description": "配置描述",
  "listingType": "rent|sale",
  "isCommercial": "false",
  "mrtStations": ["EW22", "EW23", ...],
  "propertyTypeCode": ["APT", "CONDO", ...],
  "bedrooms": ["3"],
  "minPrice": "3600",
  "maxPrice": "5500",
  "notes": ["生成信息"]
}
```

## 🔧 手动编辑配置

配置文件保存在 `configs/` 目录，可以手动编辑：

1. 打开 `configs/你的配置文件.json`
2. 修改任何字段
3. 保存文件
4. 重新运行主程序即可使用

## 📊 支持的参数

### 基础参数
- `listingType`: "rent" 或 "sale"
- `isCommercial`: "true" 或 "false"
- `mrtStations`: 地铁站代码数组
- `bedrooms`: 卧室数量数组

### 价格参数
- `minPrice` / `maxPrice`: 销售价格范围
- `minRent` / `maxRent`: 租赁价格范围

### 房产类型
- `propertyTypeCode`: ["APT", "CONDO", "EXCON", "WALK", "CLUS"]
- `propertyTypeGroup`: 通常为 "N"（非商业）

### 其他参数
- `district`: 区域代码
- `tenure`: 产权类型
- `furnishing`: 装修状态
- `floor`: 楼层要求
- `facing`: 朝向要求

## 🎉 使用新配置

1. 运行主程序: `python3 main.py`
2. 在配置列表中选择你的新配置
3. 选择测试模式或完整模式
4. 开始爬取数据

## 💡 最佳实践

### 配置命名规范
- 租赁: `rent_区域_卧室数bed`
- 销售: `sale_区域_价格范围`
- 示例: `rent_southwest_3bed`, `sale_orchard_2m3m`

### 描述建议
- 包含具体地铁站名称
- 说明价格范围
- 注明目标用户群体

### 文件名建议
- 使用英文和下划线
- 简洁但有意义
- 避免特殊字符

## 🔍 故障排除

### URL解析失败
- 确保URL完整且有效
- 检查URL中是否包含必要的参数
- 尝试重新从PropertyGuru复制URL

### 配置不显示
- 检查JSON文件格式是否正确
- 确保文件保存在 `configs/` 目录
- 重启主程序

### 爬取失败
- 验证地铁站代码是否正确
- 检查价格范围是否合理
- 确认房产类型设置正确
