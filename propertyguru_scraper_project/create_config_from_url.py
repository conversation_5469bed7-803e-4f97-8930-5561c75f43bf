#!/usr/bin/env python3
"""
从PropertyGuru URL快速创建配置文件的工具
"""

import sys
import os
sys.path.append('src')

from config_loader import ConfigLoader
import argparse


def main():
    parser = argparse.ArgumentParser(description='从PropertyGuru URL创建配置文件')
    parser.add_argument('url', help='PropertyGuru搜索URL')
    parser.add_argument('-n', '--name', help='配置名称')
    parser.add_argument('-d', '--description', help='配置描述')
    parser.add_argument('-f', '--filename', help='配置文件名（不含.json后缀）')
    parser.add_argument('--preview', action='store_true', help='仅预览配置，不保存')
    
    args = parser.parse_args()
    
    # 创建配置加载器
    loader = ConfigLoader('configs')
    
    try:
        # 从URL生成配置
        config = loader.create_config_from_url(
            url=args.url,
            config_name=args.name,
            description=args.description
        )
        
        print("🔧 从URL生成的配置:")
        print("=" * 60)
        print(f"📋 名称: {config['name']}")
        print(f"📝 描述: {config['description']}")
        print(f"🏠 类型: {config['listingType']}")
        print(f"🚇 地铁站: {len(config.get('mrtStations', []))} 个")
        if config.get('mrtStations'):
            print(f"    {', '.join(config['mrtStations'])}")
        print(f"🛏️  卧室: {', '.join(config.get('bedrooms', ['未指定']))}")
        
        # 价格信息
        if config.get('minPrice') or config.get('maxPrice'):
            min_price = config.get('minPrice', '无限制')
            max_price = config.get('maxPrice', '无限制')
            print(f"💰 价格: S${min_price} - S${max_price}")
        
        print(f"🏢 房产类型: {', '.join(config.get('propertyTypeCode', []))}")
        
        if args.preview:
            print("\n📄 完整配置预览:")
            print("-" * 60)
            import json
            print(json.dumps(config, indent=2, ensure_ascii=False))
            return
        
        # 确定文件名
        if args.filename:
            filename = args.filename
        else:
            # 自动生成文件名
            listing_type = config['listingType']
            mrt_count = len(config.get('mrtStations', []))
            bedrooms = '_'.join(config.get('bedrooms', ['any']))
            filename = f"{listing_type}_{mrt_count}mrt_{bedrooms}bed"
        
        # 保存配置
        success = loader.save_config(filename, config)
        
        if success:
            print(f"\n✅ 配置已保存为: configs/{filename}.json")
            print(f"🎯 现在可以在主程序中选择此配置进行爬取")
        else:
            print(f"\n❌ 保存配置失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def interactive_mode():
    """交互式模式"""
    print("🔧 PropertyGuru配置生成器")
    print("=" * 50)
    
    # 获取URL
    url = input("请输入PropertyGuru搜索URL: ").strip()
    if not url:
        print("❌ URL不能为空")
        return
    
    # 获取配置信息
    name = input("配置名称 (可选): ").strip()
    description = input("配置描述 (可选): ").strip()
    filename = input("文件名 (可选，不含.json): ").strip()
    
    # 创建配置
    loader = ConfigLoader('configs')
    
    try:
        config = loader.create_config_from_url(url, name, description)
        
        print("\n📋 生成的配置:")
        print("-" * 40)
        import json
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        # 确认保存
        save = input("\n是否保存此配置? (y/n): ").lower().strip()
        if save == 'y':
            if not filename:
                filename = input("请输入文件名 (不含.json): ").strip()
            
            if filename:
                success = loader.save_config(filename, config)
                if success:
                    print(f"✅ 配置已保存为: configs/{filename}.json")
                else:
                    print("❌ 保存失败")
            else:
                print("❌ 文件名不能为空")
        else:
            print("❌ 配置未保存")
            
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有参数，启动交互式模式
        interactive_mode()
    else:
        # 有参数，使用命令行模式
        main()
