#!/usr/bin/env python3
"""
测试Ready to move in状态提取
"""

import re

def test_ready_to_move_extraction():
    """测试Ready to move in状态提取"""
    
    # 测试文本样例
    test_texts = [
        "Ready to move in Condominium",
        "Ready to move in Apartment", 
        "Ready to move in HDB",
        "Ready to move in Condo",
        "Ready to move in Unit",
        "Ready to move in",
        "Ready to move in Luxury Condominium",
        "Ready to move in Executive Apartment",
        "Some text Ready to move in Apartment more text",
        "Property details Ready to move in Condominium with facilities",
        "Available from 1 Aug",
        "Available immediately",
        "Immediate",
        "Move-in ready",
        "Vacant possession"
    ]
    
    # 简化的状态模式
    status_patterns = [
        # Ready to move in 相关 - 简化匹配
        r'Ready to move in[^.]*?(?:Condominium|Apartment|HDB|Condo|Unit)',
        r'Ready to move in[^.]*',
        
        # Available 相关
        r'Available from \d{1,2} \w{3,9}',  # Available from 1 Aug
        r'Available from \w+',
        r'Available immediately',
        r'Available',
        
        # Immediate 相关
        r'Immediate',
        
        # 其他状态
        r'Move.?in ready',
        r'Vacant',
        r'Newly completed',
        r'Under construction',
        r'Coming soon'
    ]
    
    print("🧪 测试Ready to move in状态提取")
    print("=" * 60)
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n{i:2d}. 测试文本: '{text}'")
        
        status = ""
        # 按优先级尝试匹配状态
        for pattern in status_patterns:
            status_match = re.search(pattern, text, re.IGNORECASE)
            if status_match:
                status = status_match.group(0).strip()
                break
        
        if status:
            print(f"    ✅ 提取状态: '{status}'")
        else:
            print(f"    ❌ 未提取到状态")
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    test_ready_to_move_extraction()
