#!/usr/bin/env python3
"""
测试状态提取逻辑
"""

import re

def test_status_extraction():
    """测试状态提取逻辑"""
    
    # 测试文本样例
    test_texts = [
        "Ready to move in Condominium",
        "Ready to move in Apartment",
        "Ready to move in HDB",
        "Ready to move in Condo",
        "Ready to move in Unit",
        "Ready to move in Property",
        "Ready to move in",
        "Ready to move in Luxury Condominium",
        "Ready to move in Executive Apartment",
        "Ready to move in Premium Condo",
        "Ready to move in Brand new Apartment",
        "Available from 1 Aug",
        "Available from 30 Jun",
        "Available immediately",
        "Available from July",
        "Available",
        "Immediate occupancy",
        "Move-in ready",
        "Vacant possession",
        "Newly completed Condominium",
        "Under construction",
        "Coming soon",
        "Pre-launch",
        "Brand new Condominium",
        "Luxury Condominium",
        "Executive Apartment",
        "Some random text Ready to move in Condominium more text",
        "Property details Available from 15 Jul contact agent",
        "Beautiful unit Newly completed Apartment with facilities",
        "Ready to move in Apartment for rent",
        "This is Ready to move in Condominium with great facilities"
    ]
    
    # 状态提取模式 - 优化后的版本
    status_patterns = [
        # Ready to move in 相关 - 优化匹配
        r'Ready to move in\s+(?:Condominium|Apartment|HDB|Condo|Unit|Property)',
        r'Ready to move in\s+(?:Luxury|Executive|Premium|New|Brand\s+new)?\s*(?:Condominium|Apartment|HDB|Condo|Unit)',
        r'Ready to move in',  # 简单的 Ready to move in

        # Available 相关
        r'Available from\s+\d{1,2}\s+\w{3,9}(?:\s+\d{4})?',  # Available from 1 Aug 2024
        r'Available\s+(?:immediately|now|soon)',
        r'Available\s+from\s+\w+',
        r'Available',

        # Immediate 相关
        r'Immediate\s+(?:occupancy|possession|move.?in)',
        r'Immediate(?:ly)?',

        # 其他状态
        r'Move.?in ready',
        r'Vacant\s+(?:possession|unit)?',
        r'Newly completed\s+(?:Condominium|Apartment|HDB|Condo|Unit)',
        r'Newly completed',
        r'Under construction',
        r'Coming soon',
        r'Pre.?launch',

        # 房产类型状态
        r'(?:New|Brand new|Newly built)\s+(?:Condominium|Apartment|HDB|Condo|Unit)',
        r'(?:Luxury|Premium|Executive)\s+(?:Condominium|Apartment|HDB|Condo|Unit)'
    ]
    
    print("🧪 测试状态提取逻辑")
    print("=" * 60)
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n{i:2d}. 测试文本: '{text}'")
        
        status = ""
        # 按优先级尝试匹配状态
        for pattern in status_patterns:
            status_match = re.search(pattern, text, re.IGNORECASE)
            if status_match:
                status = status_match.group(0).strip()
                break
        
        if status:
            print(f"    ✅ 提取状态: '{status}'")
        else:
            print(f"    ❌ 未提取到状态")
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    test_status_extraction()
