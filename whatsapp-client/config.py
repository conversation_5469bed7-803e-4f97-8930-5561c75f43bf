#!/usr/bin/env python3
"""
WhatsApp Client Configuration
Configuration settings for PropertyGuru WhatsApp automation
"""

import os
from typing import Dict, List

# WhatsApp API Configuration
WHATSAPP_API_BASE_URL = "http://localhost:8080/api"

# CSV Processing Configuration
CSV_CONFIG = {
    'encoding': 'utf-8',
    'required_columns': ['property_url', 'contact_phone'],  # Minimum required columns
    'optional_columns': ['contact_name', 'property_title', 'property_price', 'property_address'],
    'phone_column': 'contact_phone',
    'url_column': 'property_url',
    'name_column': 'contact_name'
}

# Message Configuration
MESSAGE_CONFIG = {
    'default_template': 'property_inquiry',
    'max_message_length': 4000,  # WhatsApp message limit
    'include_property_details': True,
    'personalize_messages': True
}

# Message Templates
MESSAGE_TEMPLATES = {
    'property_inquiry': """Hi {contact_name},

I hope this message finds you well! I came across this property listing and thought it might interest you:

🏠 *Property Details:*
{property_details}

🔗 *View Full Listing:* {property_url}

Would you like to know more about this property or schedule a viewing? I'm happy to help with any questions you might have.

Best regards!""",

    'simple_inquiry': """Hi {contact_name},

I found this property that might interest you: {property_url}

{property_details}

Let me know if you'd like more information!

Best regards!""",

    'professional_inquiry': """Dear {contact_name},

I hope you're doing well. I wanted to share a property opportunity that caught my attention:

*Property Information:*
{property_details}

*Full Details:* {property_url}

If this aligns with your property interests, I'd be happy to discuss further or arrange a viewing.

Best regards,
[Your Name]"""
}

# Sending Configuration
SENDING_CONFIG = {
    'batch_size': 10,  # Number of messages to send in one batch
    'delay_between_messages': 3,  # Seconds between individual messages
    'delay_between_batches': 30,  # Seconds between batches
    'max_retries': 3,  # Maximum retry attempts for failed messages
    'retry_delay': 5,  # Seconds to wait before retrying
}

# Phone Number Formatting
PHONE_CONFIG = {
    'default_country_code': '65',  # Singapore
    'strip_characters': ['+', '-', ' ', '(', ')'],
    'required_length_min': 8,
    'required_length_max': 15
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'logs/whatsapp_client.log',
    'console_output': True
}

# Output Configuration
OUTPUT_CONFIG = {
    'results_file': 'output/sending_results_{timestamp}.csv',
    'failed_messages_file': 'output/failed_messages_{timestamp}.csv',
    'success_report_file': 'output/success_report_{timestamp}.txt',
    'timestamp_format': '%Y%m%d_%H%M%S'
}

# Property Detail Extraction
PROPERTY_CONFIG = {
    'extract_from_url': True,  # Whether to try extracting details from PropertyGuru URL
    'fallback_to_csv': True,   # Use CSV data if URL extraction fails
    'include_price': True,
    'include_address': True,
    'include_bedrooms': True,
    'include_area': True,
    'max_detail_length': 500  # Maximum length for property details section
}

# Error Handling
ERROR_CONFIG = {
    'continue_on_error': True,  # Continue processing other messages if one fails
    'log_errors': True,
    'save_failed_messages': True,
    'max_consecutive_failures': 5  # Stop if too many consecutive failures
}

def get_config_value(section: str, key: str, default=None):
    """Get configuration value with fallback to default"""
    config_map = {
        'csv': CSV_CONFIG,
        'message': MESSAGE_CONFIG,
        'sending': SENDING_CONFIG,
        'phone': PHONE_CONFIG,
        'logging': LOGGING_CONFIG,
        'output': OUTPUT_CONFIG,
        'property': PROPERTY_CONFIG,
        'error': ERROR_CONFIG
    }
    
    return config_map.get(section, {}).get(key, default)

def validate_config():
    """Validate configuration settings"""
    errors = []
    
    # Check required directories
    required_dirs = ['logs', 'output', 'templates', 'sample_data']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            try:
                os.makedirs(dir_name, exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create directory {dir_name}: {e}")
    
    # Validate message templates
    for template_name, template in MESSAGE_TEMPLATES.items():
        required_placeholders = ['{contact_name}', '{property_url}']
        for placeholder in required_placeholders:
            if placeholder not in template:
                errors.append(f"Template '{template_name}' missing required placeholder: {placeholder}")
    
    return errors

if __name__ == "__main__":
    # Test configuration validation
    errors = validate_config()
    if errors:
        print("Configuration errors found:")
        for error in errors:
            print(f"- {error}")
    else:
        print("Configuration validation passed!")
