#!/usr/bin/env python3
"""
CSV Processor for WhatsApp Client
Handles reading and processing PropertyGuru CSV files
"""

import csv
import os
import re
import logging
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
from config import CSV_CONFIG, PHONE_CONFIG

class CSVProcessor:
    """Processes CSV files containing PropertyGuru URLs and contact information"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processed_data = []
        self.errors = []
        
    def read_csv(self, file_path: str) -> List[Dict[str, str]]:
        """
        Read CSV file and return list of dictionaries
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            List of dictionaries containing row data
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV file not found: {file_path}")
        
        data = []
        try:
            with open(file_path, 'r', encoding=CSV_CONFIG['encoding']) as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                # Validate headers
                self._validate_headers(reader.fieldnames)
                
                for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is row 1)
                    # Clean and validate row data
                    cleaned_row = self._clean_row_data(row, row_num)
                    if cleaned_row:
                        data.append(cleaned_row)
                        
        except Exception as e:
            self.logger.error(f"Error reading CSV file {file_path}: {e}")
            raise
            
        self.logger.info(f"Successfully read {len(data)} rows from {file_path}")
        return data
    
    def _validate_headers(self, headers: List[str]) -> None:
        """Validate that required columns are present"""
        if not headers:
            raise ValueError("CSV file has no headers")
        
        # Convert to lowercase for case-insensitive comparison
        headers_lower = [h.lower().strip() for h in headers]
        
        missing_required = []
        for required_col in CSV_CONFIG['required_columns']:
            if required_col.lower() not in headers_lower:
                missing_required.append(required_col)
        
        if missing_required:
            raise ValueError(f"Missing required columns: {missing_required}")
        
        self.logger.info(f"CSV headers validated: {headers}")
    
    def _clean_row_data(self, row: Dict[str, str], row_num: int) -> Optional[Dict[str, str]]:
        """
        Clean and validate individual row data
        
        Args:
            row: Raw row data from CSV
            row_num: Row number for error reporting
            
        Returns:
            Cleaned row data or None if invalid
        """
        cleaned_row = {}
        errors_in_row = []
        
        # Clean all values
        for key, value in row.items():
            if value:
                cleaned_row[key.strip().lower()] = value.strip()
        
        # Validate and clean phone number
        phone_col = CSV_CONFIG['phone_column'].lower()
        if phone_col in cleaned_row:
            cleaned_phone = self._clean_phone_number(cleaned_row[phone_col])
            if cleaned_phone:
                cleaned_row[phone_col] = cleaned_phone
            else:
                errors_in_row.append(f"Invalid phone number: {cleaned_row[phone_col]}")
        else:
            errors_in_row.append(f"Missing phone number")
        
        # Validate PropertyGuru URL
        url_col = CSV_CONFIG['url_column'].lower()
        if url_col in cleaned_row:
            if not self._validate_propertyguru_url(cleaned_row[url_col]):
                errors_in_row.append(f"Invalid PropertyGuru URL: {cleaned_row[url_col]}")
        else:
            errors_in_row.append(f"Missing property URL")
        
        # Set default contact name if missing
        name_col = CSV_CONFIG['name_column'].lower()
        if name_col not in cleaned_row or not cleaned_row[name_col]:
            # Try to generate name from phone number
            if phone_col in cleaned_row:
                cleaned_row[name_col] = f"Contact {cleaned_row[phone_col][-4:]}"
            else:
                cleaned_row[name_col] = "Dear Contact"
        
        # Log errors but continue processing if not critical
        if errors_in_row:
            error_msg = f"Row {row_num}: {'; '.join(errors_in_row)}"
            self.errors.append(error_msg)
            self.logger.warning(error_msg)
            
            # Skip row if missing critical data
            if any("Missing" in error for error in errors_in_row):
                return None
        
        return cleaned_row
    
    def _clean_phone_number(self, phone: str) -> Optional[str]:
        """
        Clean and validate phone number
        
        Args:
            phone: Raw phone number string
            
        Returns:
            Cleaned phone number or None if invalid
        """
        if not phone:
            return None
        
        # Remove unwanted characters
        cleaned = phone
        for char in PHONE_CONFIG['strip_characters']:
            cleaned = cleaned.replace(char, '')
        
        # Remove any non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', cleaned)
        
        # Handle country code
        if cleaned.startswith('+'):
            cleaned = cleaned[1:]  # Remove +
        elif cleaned.startswith('00'):
            cleaned = cleaned[2:]  # Remove 00
        
        # Add default country code if needed
        if len(cleaned) == 8 and not cleaned.startswith(PHONE_CONFIG['default_country_code']):
            cleaned = PHONE_CONFIG['default_country_code'] + cleaned
        
        # Validate length
        if (len(cleaned) < PHONE_CONFIG['required_length_min'] or 
            len(cleaned) > PHONE_CONFIG['required_length_max']):
            return None
        
        # Ensure all digits
        if not cleaned.isdigit():
            return None
        
        return cleaned
    
    def _validate_propertyguru_url(self, url: str) -> bool:
        """
        Validate that URL is a PropertyGuru URL
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid PropertyGuru URL
        """
        if not url:
            return False
        
        try:
            parsed = urlparse(url)
            return (parsed.netloc.lower() in ['www.propertyguru.com.sg', 'propertyguru.com.sg'] and
                    parsed.scheme in ['http', 'https'])
        except Exception:
            return False
    
    def extract_property_details_from_csv(self, row: Dict[str, str]) -> Dict[str, str]:
        """
        Extract property details from CSV row data
        
        Args:
            row: CSV row data
            
        Returns:
            Dictionary with property details
        """
        details = {}
        
        # Map common CSV column names to property details
        column_mapping = {
            'title': ['property_title', 'title', 'name'],
            'price': ['property_price', 'price', 'cost'],
            'address': ['property_address', 'address', 'location'],
            'bedrooms': ['bedrooms', 'beds', 'bedroom'],
            'bathrooms': ['bathrooms', 'baths', 'bathroom'],
            'area': ['area', 'size', 'sqft', 'square_feet'],
            'property_type': ['property_type', 'type'],
        }
        
        for detail_key, possible_columns in column_mapping.items():
            for col in possible_columns:
                if col.lower() in row and row[col.lower()]:
                    details[detail_key] = row[col.lower()]
                    break
        
        return details
    
    def get_processing_summary(self) -> Dict[str, int]:
        """Get summary of processing results"""
        return {
            'total_processed': len(self.processed_data),
            'total_errors': len(self.errors),
            'success_rate': (len(self.processed_data) / (len(self.processed_data) + len(self.errors)) * 100) 
                           if (len(self.processed_data) + len(self.errors)) > 0 else 0
        }

def main():
    """Test the CSV processor"""
    processor = CSVProcessor()
    
    # Test with sample data
    sample_file = "sample_data/sample_properties.csv"
    if os.path.exists(sample_file):
        try:
            data = processor.read_csv(sample_file)
            print(f"Successfully processed {len(data)} rows")
            
            # Show first few rows
            for i, row in enumerate(data[:3]):
                print(f"Row {i+1}: {row}")
                
        except Exception as e:
            print(f"Error: {e}")
    else:
        print(f"Sample file not found: {sample_file}")

if __name__ == "__main__":
    main()
