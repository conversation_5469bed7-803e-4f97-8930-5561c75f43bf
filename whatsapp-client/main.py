#!/usr/bin/env python3
"""
WhatsApp Client Main Application
Processes PropertyGuru CSV files and sends WhatsApp messages
"""

import os
import sys
import csv
import logging
import argparse
from datetime import datetime
from typing import List, Dict, Optional

from config import validate_config, LOGGING_CONFIG, OUTPUT_CONFIG
from csv_processor import CSVProcessor
from message_composer import MessageComposer
from whatsapp_sender import WhatsAppSender

class WhatsAppClient:
    """Main WhatsApp client application"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.csv_processor = CSVProcessor()
        self.message_composer = MessageComposer()
        self.whatsapp_sender = WhatsAppSender()
        
        # Validate configuration
        config_errors = validate_config()
        if config_errors:
            self.logger.error("Configuration errors found:")
            for error in config_errors:
                self.logger.error(f"  - {error}")
            sys.exit(1)
    
    def setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG['level']),
            format=LOGGING_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG['file']),
                logging.StreamHandler() if LOGGING_CONFIG.get('console_output', True) else logging.NullHandler()
            ]
        )
    
    def process_csv_file(self, csv_file_path: str, template_name: str = None) -> Dict[str, any]:
        """
        Process a CSV file and send WhatsApp messages
        
        Args:
            csv_file_path: Path to the CSV file
            template_name: Message template to use
            
        Returns:
            Dictionary with processing results
        """
        self.logger.info(f"Starting to process CSV file: {csv_file_path}")
        
        try:
            # Read and validate CSV data
            csv_data = self.csv_processor.read_csv(csv_file_path)
            
            if not csv_data:
                self.logger.warning("No valid data found in CSV file")
                return {'success': False, 'error': 'No valid data in CSV file'}
            
            self.logger.info(f"Successfully loaded {len(csv_data)} records from CSV")
            
            # Prepare messages
            messages = []
            for i, row in enumerate(csv_data):
                try:
                    # Extract property details from CSV
                    property_details = self.csv_processor.extract_property_details_from_csv(row)
                    
                    # Compose message
                    message = self.message_composer.compose_message(
                        contact_data=row,
                        property_details=property_details,
                        template_name=template_name
                    )
                    
                    # Validate message
                    validation_issues = self.message_composer.validate_message(message)
                    if validation_issues:
                        self.logger.warning(f"Message validation issues for row {i+1}: {validation_issues}")
                    
                    messages.append({
                        'recipient': row.get('contact_phone'),
                        'message': message,
                        'contact_name': row.get('contact_name'),
                        'property_url': row.get('property_url'),
                        'row_number': i + 1
                    })
                    
                except Exception as e:
                    self.logger.error(f"Error preparing message for row {i+1}: {e}")
                    continue
            
            if not messages:
                self.logger.error("No valid messages could be prepared")
                return {'success': False, 'error': 'No valid messages prepared'}
            
            self.logger.info(f"Prepared {len(messages)} messages for sending")
            
            # Send messages
            sending_results = self.whatsapp_sender.send_batch_messages(messages)
            
            # Save results
            self._save_results(csv_file_path, messages, sending_results)
            
            # Prepare final results
            results = {
                'success': True,
                'csv_file': csv_file_path,
                'total_records': len(csv_data),
                'messages_prepared': len(messages),
                'sending_results': sending_results,
                'csv_processing_summary': self.csv_processor.get_processing_summary(),
                'whatsapp_stats': self.whatsapp_sender.get_sending_stats()
            }
            
            self.logger.info(f"Processing completed successfully")
            return results
            
        except Exception as e:
            self.logger.error(f"Error processing CSV file: {e}")
            return {'success': False, 'error': str(e)}
    
    def _save_results(self, csv_file_path: str, messages: List[Dict], sending_results: Dict):
        """Save processing results to files"""
        timestamp = datetime.now().strftime(OUTPUT_CONFIG['timestamp_format'])
        
        # Create output directory
        os.makedirs('output', exist_ok=True)
        
        # Save detailed results CSV
        results_file = OUTPUT_CONFIG['results_file'].format(timestamp=timestamp)
        self._save_results_csv(results_file, messages, sending_results)
        
        # Save failed messages CSV
        failed_messages = [msg for msg in messages if msg.get('failed', False)]
        if failed_messages:
            failed_file = OUTPUT_CONFIG['failed_messages_file'].format(timestamp=timestamp)
            self._save_failed_messages_csv(failed_file, failed_messages)
        
        # Save success report
        report_file = OUTPUT_CONFIG['success_report_file'].format(timestamp=timestamp)
        self._save_success_report(report_file, csv_file_path, sending_results)
    
    def _save_results_csv(self, filename: str, messages: List[Dict], sending_results: Dict):
        """Save detailed results to CSV"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['row_number', 'recipient', 'contact_name', 'property_url', 
                             'message_length', 'status', 'timestamp']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for msg in messages:
                    writer.writerow({
                        'row_number': msg.get('row_number'),
                        'recipient': msg.get('recipient'),
                        'contact_name': msg.get('contact_name'),
                        'property_url': msg.get('property_url'),
                        'message_length': len(msg.get('message', '')),
                        'status': 'Success' if msg.get('success', False) else 'Failed',
                        'timestamp': datetime.now().isoformat()
                    })
            
            self.logger.info(f"Results saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving results CSV: {e}")
    
    def _save_failed_messages_csv(self, filename: str, failed_messages: List[Dict]):
        """Save failed messages to CSV for retry"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['recipient', 'contact_name', 'property_url', 'message', 'error']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for msg in failed_messages:
                    writer.writerow({
                        'recipient': msg.get('recipient'),
                        'contact_name': msg.get('contact_name'),
                        'property_url': msg.get('property_url'),
                        'message': msg.get('message'),
                        'error': msg.get('error', 'Unknown error')
                    })
            
            self.logger.info(f"Failed messages saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving failed messages CSV: {e}")
    
    def _save_success_report(self, filename: str, csv_file: str, sending_results: Dict):
        """Save success report to text file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("WhatsApp Client Processing Report\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Processed File: {csv_file}\n")
                f.write(f"Processing Time: {datetime.now().isoformat()}\n\n")
                
                f.write("Sending Results:\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total Messages: {sending_results.get('total_messages', 0)}\n")
                f.write(f"Successful Sends: {sending_results.get('successful_sends', 0)}\n")
                f.write(f"Failed Sends: {sending_results.get('failed_sends', 0)}\n")
                f.write(f"Success Rate: {sending_results.get('success_rate', 0):.1f}%\n")
                f.write(f"Duration: {sending_results.get('duration_seconds', 0):.1f} seconds\n\n")
                
                # Add CSV processing summary
                csv_summary = self.csv_processor.get_processing_summary()
                f.write("CSV Processing Summary:\n")
                f.write("-" * 25 + "\n")
                f.write(f"Total Processed: {csv_summary.get('total_processed', 0)}\n")
                f.write(f"Total Errors: {csv_summary.get('total_errors', 0)}\n")
                f.write(f"Success Rate: {csv_summary.get('success_rate', 0):.1f}%\n")
            
            self.logger.info(f"Success report saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving success report: {e}")
    
    def test_connection(self) -> bool:
        """Test WhatsApp API connection"""
        connected, status = self.whatsapp_sender.test_connection()
        if connected:
            self.logger.info(f"WhatsApp API connection test: {status}")
        else:
            self.logger.error(f"WhatsApp API connection test failed: {status}")
        return connected
    
    def list_templates(self):
        """List available message templates"""
        templates = self.message_composer.get_available_templates()
        print("Available message templates:")
        for template in templates:
            print(f"  - {template}")
            preview = self.message_composer.preview_template(template)
            print(f"    Preview: {preview[:100]}...")
            print()

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='WhatsApp Client for PropertyGuru CSV processing')
    parser.add_argument('csv_file', nargs='?', help='Path to CSV file containing property data')
    parser.add_argument('--template', '-t', help='Message template to use')
    parser.add_argument('--test-connection', action='store_true', help='Test WhatsApp API connection')
    parser.add_argument('--list-templates', action='store_true', help='List available message templates')
    parser.add_argument('--dry-run', action='store_true', help='Process CSV but don\'t send messages')
    
    args = parser.parse_args()
    
    # Initialize client
    client = WhatsAppClient()
    
    # Handle different modes
    if args.test_connection:
        client.test_connection()
        return
    
    if args.list_templates:
        client.list_templates()
        return
    
    if not args.csv_file:
        parser.print_help()
        return
    
    if not os.path.exists(args.csv_file):
        print(f"Error: CSV file not found: {args.csv_file}")
        return
    
    # Test connection first
    if not client.test_connection():
        print("Error: Cannot connect to WhatsApp API. Please check the connection.")
        return
    
    # Process CSV file
    print(f"Processing CSV file: {args.csv_file}")
    if args.template:
        print(f"Using template: {args.template}")
    
    results = client.process_csv_file(args.csv_file, args.template)
    
    if results['success']:
        print(f"✅ Processing completed successfully!")
        print(f"📊 Results: {results['sending_results']['successful_sends']}/{results['sending_results']['total_messages']} messages sent")
        print(f"📁 Check the 'output' folder for detailed results")
    else:
        print(f"❌ Processing failed: {results.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
