#!/usr/bin/env python3
"""
Message Composer for WhatsApp Client
Handles composition of personalized property messages
"""

import re
import logging
from typing import Dict, Optional, List
from urllib.parse import urlparse, parse_qs
from config import MESSAGE_TEMPLATES, MESSAGE_CONFIG, PROPERTY_CONFIG

class MessageComposer:
    """Composes personalized WhatsApp messages for property inquiries"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def compose_message(self, 
                       contact_data: Dict[str, str], 
                       property_details: Dict[str, str] = None,
                       template_name: str = None) -> str:
        """
        Compose a personalized message for a property inquiry
        
        Args:
            contact_data: Dictionary containing contact information
            property_details: Dictionary containing property details
            template_name: Name of the message template to use
            
        Returns:
            Composed message string
        """
        if not template_name:
            template_name = MESSAGE_CONFIG['default_template']
        
        template = MESSAGE_TEMPLATES.get(template_name)
        if not template:
            self.logger.warning(f"Template '{template_name}' not found, using default")
            template = MESSAGE_TEMPLATES[MESSAGE_CONFIG['default_template']]
        
        # Prepare message variables
        message_vars = self._prepare_message_variables(contact_data, property_details)
        
        try:
            # Format the message
            message = template.format(**message_vars)
            
            # Validate message length
            if len(message) > MESSAGE_CONFIG['max_message_length']:
                self.logger.warning(f"Message too long ({len(message)} chars), truncating")
                message = self._truncate_message(message)
            
            return message
            
        except KeyError as e:
            self.logger.error(f"Missing template variable: {e}")
            return self._create_fallback_message(contact_data, property_details)
    
    def _prepare_message_variables(self, 
                                  contact_data: Dict[str, str], 
                                  property_details: Dict[str, str] = None) -> Dict[str, str]:
        """Prepare variables for message template formatting"""
        
        # Start with contact data
        variables = {
            'contact_name': contact_data.get('contact_name', 'Dear Contact'),
            'property_url': contact_data.get('property_url', ''),
        }
        
        # Add property details
        if property_details:
            variables.update(property_details)
        
        # Format property details section
        variables['property_details'] = self._format_property_details(property_details or {})
        
        return variables
    
    def _format_property_details(self, details: Dict[str, str]) -> str:
        """Format property details into a readable string"""
        if not details:
            return "Property details will be available in the full listing."
        
        formatted_parts = []
        
        # Define the order and formatting for different details
        detail_formatters = {
            'title': lambda x: f"📍 *{x}*",
            'price': lambda x: f"💰 Price: {x}",
            'address': lambda x: f"📍 Location: {x}",
            'bedrooms': lambda x: f"🛏️ Bedrooms: {x}",
            'bathrooms': lambda x: f"🚿 Bathrooms: {x}",
            'area': lambda x: f"📐 Area: {x}",
            'property_type': lambda x: f"🏠 Type: {x}",
        }
        
        # Format each available detail
        for key, formatter in detail_formatters.items():
            if key in details and details[key]:
                formatted_parts.append(formatter(details[key]))
        
        # Join with newlines and limit length
        formatted = '\n'.join(formatted_parts)
        
        if len(formatted) > PROPERTY_CONFIG['max_detail_length']:
            # Truncate and add ellipsis
            formatted = formatted[:PROPERTY_CONFIG['max_detail_length']-3] + '...'
        
        return formatted if formatted else "Property details available in the listing."
    
    def _truncate_message(self, message: str) -> str:
        """Truncate message to fit within WhatsApp limits"""
        max_length = MESSAGE_CONFIG['max_message_length'] - 50  # Leave some buffer
        
        if len(message) <= max_length:
            return message
        
        # Try to truncate at a natural break point
        truncated = message[:max_length]
        
        # Find the last sentence or line break
        last_period = truncated.rfind('.')
        last_newline = truncated.rfind('\n')
        
        break_point = max(last_period, last_newline)
        
        if break_point > max_length * 0.8:  # If break point is reasonably close to end
            truncated = message[:break_point + 1]
        
        return truncated + "\n\n[Message truncated - see full details in the link]"
    
    def _create_fallback_message(self, 
                                contact_data: Dict[str, str], 
                                property_details: Dict[str, str] = None) -> str:
        """Create a simple fallback message when template formatting fails"""
        contact_name = contact_data.get('contact_name', 'Dear Contact')
        property_url = contact_data.get('property_url', '')
        
        message = f"Hi {contact_name},\n\n"
        message += "I found a property that might interest you:\n\n"
        
        if property_details:
            message += self._format_property_details(property_details) + "\n\n"
        
        message += f"View full details: {property_url}\n\n"
        message += "Let me know if you'd like more information!\n\n"
        message += "Best regards!"
        
        return message
    
    def extract_property_id_from_url(self, url: str) -> Optional[str]:
        """Extract property ID from PropertyGuru URL"""
        try:
            # PropertyGuru URLs typically end with a property ID
            # Example: https://www.propertyguru.com.sg/listing/for-sale-bartley-residences-25585441
            match = re.search(r'-(\d+)$', url)
            if match:
                return match.group(1)
            
            # Alternative: check query parameters
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            if 'id' in query_params:
                return query_params['id'][0]
                
        except Exception as e:
            self.logger.warning(f"Could not extract property ID from URL {url}: {e}")
        
        return None
    
    def validate_message(self, message: str) -> List[str]:
        """
        Validate composed message for common issues
        
        Returns:
            List of validation warnings/errors
        """
        issues = []
        
        if not message.strip():
            issues.append("Message is empty")
            return issues
        
        if len(message) > MESSAGE_CONFIG['max_message_length']:
            issues.append(f"Message too long: {len(message)} characters")
        
        # Check for placeholder variables that weren't replaced
        placeholder_pattern = r'\{[^}]+\}'
        unresolved_placeholders = re.findall(placeholder_pattern, message)
        if unresolved_placeholders:
            issues.append(f"Unresolved placeholders: {unresolved_placeholders}")
        
        # Check for essential components
        if 'http' not in message.lower():
            issues.append("Message doesn't contain a URL")
        
        # Check for very short messages
        if len(message.strip()) < 50:
            issues.append("Message might be too short")
        
        return issues
    
    def get_available_templates(self) -> List[str]:
        """Get list of available message templates"""
        return list(MESSAGE_TEMPLATES.keys())
    
    def preview_template(self, template_name: str) -> str:
        """Get a preview of a template with sample data"""
        if template_name not in MESSAGE_TEMPLATES:
            return f"Template '{template_name}' not found"
        
        sample_data = {
            'contact_name': 'John Doe',
            'property_url': 'https://www.propertyguru.com.sg/listing/sample-property-12345',
            'property_details': '🏠 *Beautiful 3-Bedroom Condo*\n💰 Price: S$ 1,200,000\n📍 Location: Orchard Road\n🛏️ Bedrooms: 3\n🚿 Bathrooms: 2\n📐 Area: 1,200 sqft'
        }
        
        try:
            return MESSAGE_TEMPLATES[template_name].format(**sample_data)
        except Exception as e:
            return f"Error previewing template: {e}"

def main():
    """Test the message composer"""
    composer = MessageComposer()
    
    # Test data
    contact_data = {
        'contact_name': 'Alice Smith',
        'property_url': 'https://www.propertyguru.com.sg/listing/for-sale-bartley-residences-25585441',
        'contact_phone': '6591234567'
    }
    
    property_details = {
        'title': 'Bartley Residences',
        'price': 'S$ 2,100,000',
        'address': '16 min (1.37 km) from NE12 Serangoon MRT Station',
        'area': '1,066 sqft'
    }
    
    # Test message composition
    message = composer.compose_message(contact_data, property_details)
    print("Composed Message:")
    print("=" * 50)
    print(message)
    print("=" * 50)
    
    # Test validation
    issues = composer.validate_message(message)
    if issues:
        print(f"Validation issues: {issues}")
    else:
        print("Message validation passed!")
    
    # Show available templates
    print(f"\nAvailable templates: {composer.get_available_templates()}")

if __name__ == "__main__":
    main()
