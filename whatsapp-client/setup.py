#!/usr/bin/env python3
"""
Setup script for WhatsApp Client
Creates necessary directories and validates setup
"""

import os
import sys
import subprocess

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'output', 'sample_data', 'templates']
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    try:
        print("Installing Python dependencies...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ pip not found. Please install pip first.")
        return False

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python 3.7+ required, found {version.major}.{version.minor}")
        return False
    else:
        print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
        return True

def run_tests():
    """Run the test suite"""
    try:
        print("\nRunning test suite...")
        result = subprocess.run([sys.executable, 'test_client.py'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Some tests failed")
            return False
            
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def main():
    """Main setup function"""
    print("WhatsApp Client Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create directories
    if not create_directories():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Run tests
    if not run_tests():
        print("\n⚠️  Setup completed but tests failed.")
        print("You may still be able to use the client, but some features might not work.")
        return 1
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the whatsapp-mcp-server:")
    print("   cd ../whatsapp-mcp-server && python main.py")
    print("2. Prepare your CSV file with property data")
    print("3. Test the connection:")
    print("   python main.py --test-connection")
    print("4. Process your CSV:")
    print("   python main.py your_properties.csv")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
