#!/usr/bin/env python3
"""
Test script for WhatsApp Client
Tests all components without sending actual messages
"""

import os
import sys
import logging
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import validate_config
from csv_processor import CSVProcessor
from message_composer import MessageComposer
from whatsapp_sender import WhatsAppSender

def test_configuration():
    """Test configuration validation"""
    print("Testing configuration...")
    errors = validate_config()
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ Configuration validation passed")
        return True

def test_csv_processor():
    """Test CSV processing"""
    print("\nTesting CSV processor...")
    processor = CSVProcessor()
    
    sample_file = "sample_data/sample_properties.csv"
    if not os.path.exists(sample_file):
        print(f"❌ Sample file not found: {sample_file}")
        return False
    
    try:
        data = processor.read_csv(sample_file)
        print(f"✅ Successfully read {len(data)} rows from CSV")
        
        # Test property detail extraction
        if data:
            details = processor.extract_property_details_from_csv(data[0])
            print(f"✅ Property details extracted: {len(details)} fields")
            
        summary = processor.get_processing_summary()
        print(f"✅ Processing summary: {summary}")
        return True
        
    except Exception as e:
        print(f"❌ CSV processing failed: {e}")
        return False

def test_message_composer():
    """Test message composition"""
    print("\nTesting message composer...")
    composer = MessageComposer()
    
    # Test data
    contact_data = {
        'contact_name': 'Test User',
        'property_url': 'https://www.propertyguru.com.sg/listing/for-sale-test-property-12345',
        'contact_phone': '6591234567'
    }
    
    property_details = {
        'title': 'Test Property',
        'price': 'S$ 1,500,000',
        'address': 'Test Location, Singapore',
        'bedrooms': '3',
        'area': '1,200 sqft'
    }
    
    try:
        # Test message composition
        message = composer.compose_message(contact_data, property_details)
        print(f"✅ Message composed successfully ({len(message)} characters)")
        
        # Test validation
        issues = composer.validate_message(message)
        if issues:
            print(f"⚠️  Message validation issues: {issues}")
        else:
            print("✅ Message validation passed")
        
        # Test templates
        templates = composer.get_available_templates()
        print(f"✅ Available templates: {templates}")
        
        return True
        
    except Exception as e:
        print(f"❌ Message composition failed: {e}")
        return False

def test_whatsapp_sender():
    """Test WhatsApp sender (connection only)"""
    print("\nTesting WhatsApp sender...")
    sender = WhatsAppSender()
    
    try:
        # Test connection
        connected, status = sender.test_connection()
        if connected:
            print(f"✅ WhatsApp API connection: {status}")
        else:
            print(f"⚠️  WhatsApp API connection: {status}")
            print("   Note: This is expected if whatsapp-mcp-server is not running")
        
        # Test stats
        stats = sender.get_sending_stats()
        print(f"✅ Sending stats initialized: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ WhatsApp sender test failed: {e}")
        return False

def test_full_workflow():
    """Test the complete workflow without sending"""
    print("\nTesting full workflow...")
    
    try:
        # Initialize components
        processor = CSVProcessor()
        composer = MessageComposer()
        
        # Read sample CSV
        sample_file = "sample_data/sample_properties.csv"
        if not os.path.exists(sample_file):
            print(f"❌ Sample file not found: {sample_file}")
            return False
        
        csv_data = processor.read_csv(sample_file)
        print(f"✅ Loaded {len(csv_data)} records from CSV")
        
        # Process each record
        messages_prepared = 0
        for i, row in enumerate(csv_data):
            # Extract property details
            property_details = processor.extract_property_details_from_csv(row)
            
            # Compose message
            message = composer.compose_message(row, property_details)
            
            # Validate message
            issues = composer.validate_message(message)
            if not issues:
                messages_prepared += 1
            
            print(f"  Row {i+1}: Message prepared ({'✅' if not issues else '⚠️'})")
        
        print(f"✅ Full workflow test completed: {messages_prepared}/{len(csv_data)} messages ready")
        return True
        
    except Exception as e:
        print(f"❌ Full workflow test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("WhatsApp Client Test Suite")
    print("=" * 50)
    
    # Setup basic logging
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("Configuration", test_configuration),
        ("CSV Processor", test_csv_processor),
        ("Message Composer", test_message_composer),
        ("WhatsApp Sender", test_whatsapp_sender),
        ("Full Workflow", test_full_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} test failed")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! WhatsApp Client is ready to use.")
        print("\nNext steps:")
        print("1. Start the whatsapp-mcp-server")
        print("2. Prepare your CSV file with property data")
        print("3. Run: python main.py your_file.csv")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
